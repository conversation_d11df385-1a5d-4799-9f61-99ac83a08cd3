#!/usr/bin/env python3
"""
vLLM Paris vs London 极端竞态条件测试
===================================

基于用户的Paris/London案例，使用极端条件尝试触发竞态条件：
1. 极高并发度
2. 快速切换对立的logit_bias
3. 混合不同强度的bias
"""

import requests
import json
import time
import threading
import concurrent.futures
from typing import List, Dict, Any
import argparse

class ExtremeParisLondonRaceTester:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "messages": [
                {
                    "role": "user",
                    "content": "What is the capital of France?"
                }
            ],
            "max_tokens": 50,
            "temperature": 0.0,
            "seed": 42
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def send_request(self, payload: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """发送请求并分析结果"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                resp_data = response.json()
                content = resp_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # 分析Paris/London
                mentions_paris = "Paris" in content
                mentions_london = "London" in content
                
                if mentions_paris and not mentions_london:
                    answer_type = "paris_only"
                elif mentions_london and not mentions_paris:
                    answer_type = "london_only"
                elif mentions_paris and mentions_london:
                    answer_type = "both"
                else:
                    answer_type = "neither"
                
                return {
                    "request_id": request_id,
                    "success": True,
                    "content": content,
                    "answer_type": answer_type,
                    "mentions_paris": mentions_paris,
                    "mentions_london": mentions_london,
                    "execution_time": execution_time,
                    "timestamp": start_time,
                    "logit_bias": payload.get("logit_bias", {})
                }
            else:
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "execution_time": execution_time,
                    "logit_bias": payload.get("logit_bias", {})
                }
                
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time,
                "logit_bias": payload.get("logit_bias", {})
            }
    
    def test_extreme_concurrent_paris_london(self):
        """极高并发测试Paris vs London"""
        self.log("🎯 极高并发Paris vs London测试")
        self.log("="*60)
        
        # 用户的原始配置
        user_bias = {
            "59604": -100,  # 避免Paris
            "12095": -100,  # 避免Paris
            "39572": 5      # 偏向London
        }
        
        payload = {
            **self.base_payload,
            "logit_bias": user_bias
        }
        
        self.log("📤 使用用户的logit_bias配置")
        self.log(f"   {user_bias}")
        self.log("🔄 启动100个并发线程...")
        
        results = []
        
        def worker(i: int):
            result = self.send_request(payload, f"extreme-{i}")
            results.append(result)
        
        # 启动100个并发线程
        with concurrent.futures.ThreadPoolExecutor(max_workers=100) as executor:
            futures = [executor.submit(worker, i) for i in range(100)]
            concurrent.futures.wait(futures, timeout=180)
        
        # 分析极高并发结果
        self.analyze_extreme_concurrent_results(results)
    
    def analyze_extreme_concurrent_results(self, results: List[Dict]):
        """分析极高并发结果"""
        successful_results = [r for r in results if r.get("success", False)]
        
        self.log(f"\n📊 极高并发测试结果:")
        self.log(f"   总请求: {len(results)}")
        self.log(f"   成功请求: {len(successful_results)}")
        self.log(f"   失败请求: {len(results) - len(successful_results)}")
        
        if len(successful_results) < 50:
            self.log("❌ 成功请求太少，无法分析")
            return
        
        # 统计答案类型
        answer_types = [r['answer_type'] for r in successful_results]
        unique_types = set(answer_types)
        
        self.log(f"   唯一答案类型: {len(unique_types)}")
        
        for answer_type in unique_types:
            count = answer_types.count(answer_type)
            percentage = (count / len(successful_results)) * 100
            self.log(f"      {answer_type}: {count}次 ({percentage:.1f}%)")
        
        # 分析执行时间
        execution_times = [r['execution_time'] for r in successful_results]
        min_time = min(execution_times)
        max_time = max(execution_times)
        avg_time = sum(execution_times) / len(execution_times)
        
        self.log(f"   执行时间: {min_time:.3f}s - {max_time:.3f}s (平均: {avg_time:.3f}s)")
        self.log(f"   时间方差: {max_time - min_time:.3f}s")
        
        # 检查竞态条件
        race_detected = False
        
        if len(unique_types) > 1:
            self.log("🚨 发现答案类型不一致!", "CRITICAL")
            self.log("💥 相同logit_bias在高并发下产生了不同答案类型")
            race_detected = True
            
            # 显示不同答案的例子
            for answer_type in unique_types:
                example = next(r for r in successful_results if r['answer_type'] == answer_type)
                self.log(f"   {answer_type}示例: '{example['content'][:50]}...'")
        
        if max_time - min_time > 2.0:
            self.log("🚨 发现执行时间严重不一致!", "CRITICAL")
            self.log("💥 高并发导致异步操作同步问题")
            race_detected = True
        
        return race_detected
    
    def test_rapid_opposing_bias_switching(self):
        """快速切换对立bias配置"""
        self.log("\n🎯 快速切换对立bias配置")
        self.log("="*60)
        
        # 创建对立的配置
        opposing_configs = [
            {
                "name": "极强London",
                "bias": {"59604": -100, "12095": -100, "39572": 50},
                "expected": "london_only"
            },
            {
                "name": "极强Paris",
                "bias": {"59604": 50, "12095": 50, "39572": -100},
                "expected": "paris_only"
            }
        ]
        
        self.log("📤 对立配置:")
        for config in opposing_configs:
            self.log(f"   {config['name']}: {config['bias']} -> 预期: {config['expected']}")
        
        self.log("\n🔄 快速交替发送100个请求...")
        
        results = []
        
        # 快速交替发送100个请求
        for i in range(100):
            config_idx = i % 2
            config = opposing_configs[config_idx]
            
            payload = {
                **self.base_payload,
                "logit_bias": config["bias"]
            }
            
            result = self.send_request(payload, f"opposing-{config['name']}-{i}")
            results.append((config_idx, result))
            
            time.sleep(0.005)  # 5ms间隔，极快切换
        
        # 分析对立切换结果
        self.analyze_opposing_bias_results(results, opposing_configs)
    
    def analyze_opposing_bias_results(self, results: List[tuple], configs: List[Dict]):
        """分析对立bias切换结果"""
        self.log(f"\n📊 对立bias切换结果:")
        
        # 按配置分组
        config_groups = {0: [], 1: []}
        
        for config_idx, result in results:
            if result.get("success", False):
                config_groups[config_idx].append(result)
        
        # 分析每个配置组
        race_detected = False
        
        for config_idx, group_results in config_groups.items():
            if not group_results:
                continue
            
            config = configs[config_idx]
            config_name = config["name"]
            expected = config["expected"]
            
            answer_types = [r['answer_type'] for r in group_results]
            unique_types = set(answer_types)
            
            self.log(f"\n   {config_name} (50次请求):")
            self.log(f"      成功请求: {len(group_results)}")
            self.log(f"      答案类型数: {len(unique_types)}")
            self.log(f"      预期类型: {expected}")
            
            for answer_type in unique_types:
                count = answer_types.count(answer_type)
                percentage = (count / len(group_results)) * 100
                marker = "✅" if answer_type == expected else "❌"
                self.log(f"         {marker} {answer_type}: {count}次 ({percentage:.1f}%)")
            
            # 检查内部一致性
            if len(unique_types) > 1:
                self.log(f"🚨 {config_name}内部不一致!", "CRITICAL")
                race_detected = True
            
            # 检查是否符合预期
            if expected not in unique_types or len(unique_types) > 1:
                self.log(f"🚨 {config_name}效果异常!", "CRITICAL")
                race_detected = True
        
        # 检查对立配置是否产生了相同输出
        if len(config_groups[0]) > 0 and len(config_groups[1]) > 0:
            contents_0 = set(r['content'] for r in config_groups[0])
            contents_1 = set(r['content'] for r in config_groups[1])
            
            common_contents = contents_0.intersection(contents_1)
            if common_contents:
                self.log(f"🚨 对立配置产生了相同输出!", "CRITICAL")
                self.log(f"   相同输出数: {len(common_contents)}")
                self.log(f"   示例: '{list(common_contents)[0][:50]}...'")
                race_detected = True
        
        return race_detected
    
    def test_mixed_bias_strength_interference(self):
        """测试混合bias强度的相互干扰"""
        self.log("\n🎯 测试混合bias强度干扰")
        self.log("="*60)
        
        # 不同强度的bias配置
        bias_configs = [
            {"name": "弱London", "bias": {"39572": 1}, "expected": "可能london"},
            {"name": "中London", "bias": {"39572": 10}, "expected": "应该london"},
            {"name": "强London", "bias": {"39572": 50}, "expected": "必须london"},
            {"name": "弱避免Paris", "bias": {"59604": -1}, "expected": "可能避免paris"},
            {"name": "强避免Paris", "bias": {"59604": -50}, "expected": "必须避免paris"},
        ]
        
        self.log("📤 混合强度配置:")
        for config in bias_configs:
            self.log(f"   {config['name']}: {config['bias']}")
        
        results = []
        
        # 同时发送不同强度的请求
        def worker(config_idx: int, request_id: int):
            config = bias_configs[config_idx]
            payload = {
                **self.base_payload,
                "logit_bias": config["bias"]
            }
            result = self.send_request(payload, f"mixed-{config['name']}-{request_id}")
            results.append((config_idx, result))
        
        # 使用线程池同时发送
        with concurrent.futures.ThreadPoolExecutor(max_workers=25) as executor:
            futures = []
            
            # 每种配置发送5个请求
            for config_idx in range(len(bias_configs)):
                for request_id in range(5):
                    future = executor.submit(worker, config_idx, request_id)
                    futures.append(future)
            
            concurrent.futures.wait(futures, timeout=60)
        
        # 分析混合强度结果
        self.analyze_mixed_strength_results(results, bias_configs)
    
    def analyze_mixed_strength_results(self, results: List[tuple], configs: List[Dict]):
        """分析混合强度结果"""
        self.log(f"\n📊 混合bias强度结果:")
        
        # 按配置分组
        config_groups = {}
        for i in range(len(configs)):
            config_groups[i] = []
        
        for config_idx, result in results:
            if result.get("success", False):
                config_groups[config_idx].append(result)
        
        # 分析每个强度配置
        for config_idx, group_results in config_groups.items():
            if not group_results:
                continue
            
            config = configs[config_idx]
            config_name = config["name"]
            
            answer_types = [r['answer_type'] for r in group_results]
            unique_types = set(answer_types)
            
            self.log(f"\n   {config_name}:")
            self.log(f"      成功请求: {len(group_results)}")
            
            for answer_type in unique_types:
                count = answer_types.count(answer_type)
                self.log(f"         {answer_type}: {count}次")
            
            # 检查内部一致性
            if len(unique_types) > 1:
                self.log(f"🚨 {config_name}内部不一致!", "CRITICAL")
    
    def run_extreme_paris_london_race_test(self):
        """运行极端Paris vs London竞态条件测试"""
        self.log("🚀 vLLM 极端Paris vs London竞态条件测试")
        self.log("="*70)
        self.log("基于用户合理案例的极端测试")
        self.log("="*70)
        
        # 测试1: 极高并发
        race1 = self.test_extreme_concurrent_paris_london()
        
        # 测试2: 快速对立切换
        race2 = self.test_rapid_opposing_bias_switching()
        
        # 测试3: 混合强度干扰
        self.test_mixed_bias_strength_interference()
        
        self.log("\n" + "="*70)
        self.log("🎯 极端Paris vs London测试总结")
        self.log("="*70)
        self.log("您的Paris/London例子确实更合理，因为:")
        self.log("1. ✅ 有明确的预期 (Paris vs London)")
        self.log("2. ✅ 容易验证结果")
        self.log("3. ✅ 基于真实场景")
        self.log("4. ✅ 错误很明显")
        
        total_races = sum([race1 or False, race2 or False])
        if total_races > 0:
            self.log(f"\n🚨 在极端条件下发现了竞态条件! ({total_races}/2)")
        else:
            self.log(f"\n✅ 即使在极端条件下也未发现明显竞态条件")
            self.log("   这可能说明vLLM的logit_bias实现相对稳定")

def main():
    parser = argparse.ArgumentParser(description='vLLM极端Paris vs London竞态条件测试')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    
    args = parser.parse_args()
    
    tester = ExtremeParisLondonRaceTester(args.target)
    tester.run_extreme_paris_london_race_test()

if __name__ == "__main__":
    main()
