#!/usr/bin/env python3
"""
vLLM OpenAI API 参数漏洞测试
===========================

基于数据流分析，测试OpenAI API参数中的安全漏洞：
1. logit_bias竞态条件
2. seed处理不一致
3. 参数边界检查绕过
4. 特殊值处理缺失
"""

import requests
import json
import time
import threading
import math
from typing import List, Dict, Any, Optional
import argparse

class OpenAIParamsVulnTester:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "messages": [
                {
                    "role": "user",
                    "content": "Say hello"
                }
            ],
            "max_tokens": 10
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def send_request(self, payload: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """发送请求并返回结果"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            execution_time = time.time() - start_time
            
            return {
                "request_id": request_id,
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": response.json() if response.status_code == 200 else response.text,
                "execution_time": execution_time,
                "timestamp": start_time
            }
                
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time
            }
    
    def test_logit_bias_race_condition(self):
        """测试logit_bias竞态条件"""
        self.log("🎯 测试logit_bias竞态条件")
        self.log("="*60)
        
        # 使用您的Paris/London案例
        race_payload = {
            **self.base_payload,
            "messages": [{"role": "user", "content": "What is the capital of France?"}],
            "logit_bias": {
                "59604": -100,  # 避免Paris
                "12095": -100,  # 避免Paris
                "39572": 5      # 偏向London
            },
            "temperature": 0.0,
            "seed": 42
        }
        
        self.log("📤 竞态测试载荷:")
        print(json.dumps(race_payload, indent=2))
        
        self.log("\n🔄 发送50个并发请求测试竞态条件...")
        
        results = []
        threads = []
        
        def worker(i: int):
            result = self.send_request(race_payload, f"race-{i}")
            results.append(result)
        
        # 50个并发线程
        for i in range(50):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 分析竞态结果
        self.analyze_race_condition_results(results)
    
    def analyze_race_condition_results(self, results: List[Dict]):
        """分析竞态条件结果"""
        successful_results = [r for r in results if r.get("success", False)]
        
        self.log(f"\n📊 logit_bias竞态条件分析:")
        self.log(f"   成功请求: {len(successful_results)}/{len(results)}")
        
        if len(successful_results) < 10:
            self.log("❌ 成功请求太少，无法分析")
            return
        
        # 分析输出一致性
        contents = []
        for result in successful_results:
            content = result['response']['choices'][0]['message']['content']
            contents.append(content)
        
        unique_contents = set(contents)
        
        self.log(f"   唯一输出: {len(unique_contents)}")
        
        for i, content in enumerate(unique_contents):
            count = contents.count(content)
            percentage = (count / len(successful_results)) * 100
            self.log(f"      输出{i+1}: '{content[:50]}...' ({count}次, {percentage:.1f}%)")
        
        # 分析执行时间
        execution_times = [r['execution_time'] for r in successful_results]
        min_time = min(execution_times)
        max_time = max(execution_times)
        time_variance = max_time - min_time
        
        self.log(f"   执行时间: {min_time:.3f}s - {max_time:.3f}s")
        self.log(f"   时间方差: {time_variance:.3f}s")
        
        # 检测竞态条件
        if len(unique_contents) > 1:
            self.log("🚨 发现logit_bias竞态条件!", "CRITICAL")
            self.log("💥 相同输入产生了不同输出")
        
        if time_variance > 1.0:
            self.log("🚨 发现执行时间异常!", "CRITICAL")
            self.log("💥 异步操作存在同步问题")
    
    def test_seed_inconsistency(self):
        """测试seed处理不一致"""
        self.log("\n🎯 测试seed处理不一致")
        self.log("="*60)
        
        test_cases = [
            {"name": "无seed", "seed": None},
            {"name": "seed=42", "seed": 42},
            {"name": "seed=0", "seed": 0},
            {"name": "seed=-1", "seed": -1},
        ]
        
        for case in test_cases:
            self.log(f"\n📤 测试案例: {case['name']}")
            
            payload = {
                **self.base_payload,
                "temperature": 0.8  # 使用非零温度观察随机性
            }
            
            if case['seed'] is not None:
                payload['seed'] = case['seed']
            
            # 发送3个相同请求
            results = []
            for i in range(3):
                result = self.send_request(payload, f"seed-{case['name']}-{i}")
                results.append(result)
                time.sleep(0.1)
            
            # 分析seed效果
            self.analyze_seed_results(case, results)
    
    def analyze_seed_results(self, case: Dict, results: List[Dict]):
        """分析seed结果"""
        successful_results = [r for r in results if r.get("success", False)]
        
        if len(successful_results) < 2:
            self.log("❌ 成功请求太少")
            return
        
        contents = []
        for result in successful_results:
            content = result['response']['choices'][0]['message']['content']
            contents.append(content)
        
        unique_contents = set(contents)
        
        self.log(f"   成功请求: {len(successful_results)}")
        self.log(f"   唯一输出: {len(unique_contents)}")
        
        # 分析seed行为
        if case['seed'] is not None and case['seed'] >= 0:
            # 有效seed应该产生一致输出
            if len(unique_contents) > 1:
                self.log("🚨 seed确定性失效!", "CRITICAL")
                self.log("💥 相同seed产生了不同输出")
            else:
                self.log("✅ seed确定性正常")
        else:
            # 无seed或负seed应该产生随机输出
            if len(unique_contents) == 1:
                self.log("⚠️  可能的seed处理异常")
                self.log("💡 无seed时应该有随机性")
    
    def test_parameter_boundary_bypass(self):
        """测试参数边界检查绕过"""
        self.log("\n🎯 测试参数边界检查绕过")
        self.log("="*60)
        
        boundary_test_cases = [
            {
                "name": "temperature特殊值",
                "params": {"temperature": float('nan')},
                "expected": "应该被拒绝"
            },
            {
                "name": "temperature无穷大",
                "params": {"temperature": float('inf')},
                "expected": "应该被拒绝"
            },
            {
                "name": "max_tokens溢出",
                "params": {"max_tokens": 2**31},
                "expected": "应该被拒绝"
            },
            {
                "name": "max_tokens负数",
                "params": {"max_tokens": -1},
                "expected": "应该被拒绝"
            },
            {
                "name": "top_p超范围",
                "params": {"top_p": 2.0},
                "expected": "应该被拒绝"
            },
            {
                "name": "frequency_penalty超范围",
                "params": {"frequency_penalty": 5.0},
                "expected": "应该被拒绝"
            },
            {
                "name": "logit_bias超大token",
                "params": {"logit_bias": {"999999999": 1.0}},
                "expected": "应该被拒绝"
            }
        ]
        
        for case in boundary_test_cases:
            self.log(f"\n📤 测试: {case['name']}")
            
            payload = {**self.base_payload, **case['params']}
            
            # 特殊处理NaN和Inf（JSON不支持）
            if case['name'] in ["temperature特殊值", "temperature无穷大"]:
                self.test_special_float_values(case)
                continue
            
            result = self.send_request(payload, f"boundary-{case['name']}")
            
            self.log(f"   参数: {case['params']}")
            self.log(f"   预期: {case['expected']}")
            
            if result['success']:
                self.log("🚨 边界检查绕过!", "CRITICAL")
                self.log("💥 无效参数被接受")
            else:
                self.log("✅ 边界检查正常")
                self.log(f"   错误: {result.get('response', 'Unknown error')[:100]}")
    
    def test_special_float_values(self, case: Dict):
        """测试特殊浮点值（需要特殊处理）"""
        self.log(f"   {case['name']}: 需要特殊测试方法")
        self.log("   💡 JSON不支持NaN/Inf，需要其他方式测试")
    
    def test_stop_sequence_vulnerabilities(self):
        """测试stop序列漏洞"""
        self.log("\n🎯 测试stop序列漏洞")
        self.log("="*60)
        
        stop_test_cases = [
            {
                "name": "空字符串stop",
                "stop": [""],
                "expected": "可能导致缓冲区问题"
            },
            {
                "name": "超长stop序列",
                "stop": ["A" * 10000],
                "expected": "可能导致内存问题"
            },
            {
                "name": "大量stop序列",
                "stop": [f"stop{i}" for i in range(1000)],
                "expected": "可能导致性能问题"
            },
            {
                "name": "特殊字符stop",
                "stop": ["\x00", "\xff", "🚨"],
                "expected": "可能导致编码问题"
            }
        ]
        
        for case in stop_test_cases:
            self.log(f"\n📤 测试: {case['name']}")
            
            payload = {
                **self.base_payload,
                "stop": case['stop'],
                "max_tokens": 50
            }
            
            result = self.send_request(payload, f"stop-{case['name']}")
            
            self.log(f"   stop序列数: {len(case['stop'])}")
            self.log(f"   预期: {case['expected']}")
            
            if result['success']:
                self.log("✅ stop序列被接受")
                execution_time = result['execution_time']
                if execution_time > 5.0:
                    self.log("⚠️  执行时间异常长", "WARNING")
            else:
                self.log("❌ stop序列被拒绝")
                self.log(f"   错误: {result.get('response', 'Unknown')[:100]}")
    
    def run_comprehensive_openai_params_test(self):
        """运行全面的OpenAI API参数漏洞测试"""
        self.log("🚀 vLLM OpenAI API 参数漏洞测试")
        self.log("="*70)
        self.log("基于数据流分析的安全测试")
        self.log("="*70)
        
        # 测试1: logit_bias竞态条件
        self.test_logit_bias_race_condition()
        
        # 测试2: seed处理不一致
        self.test_seed_inconsistency()
        
        # 测试3: 参数边界检查绕过
        self.test_parameter_boundary_bypass()
        
        # 测试4: stop序列漏洞
        self.test_stop_sequence_vulnerabilities()
        
        self.log("\n" + "="*70)
        self.log("🎯 OpenAI API参数漏洞测试总结")
        self.log("="*70)
        self.log("测试完成！检查上述输出中的🚨标记以识别漏洞")
        self.log("\n主要关注点:")
        self.log("1. 🚨 logit_bias竞态条件 - 相同输入不同输出")
        self.log("2. 🚨 seed确定性失效 - 固定seed产生随机输出")
        self.log("3. 🚨 边界检查绕过 - 无效参数被接受")
        self.log("4. ⚠️  性能异常 - 执行时间过长")

def main():
    parser = argparse.ArgumentParser(description='vLLM OpenAI API参数漏洞测试工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    
    args = parser.parse_args()
    
    tester = OpenAIParamsVulnTester(args.target)
    tester.run_comprehensive_openai_params_test()

if __name__ == "__main__":
    main()
